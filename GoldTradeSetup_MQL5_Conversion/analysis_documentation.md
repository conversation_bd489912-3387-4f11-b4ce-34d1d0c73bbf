# Gold Trade Setup Strategy - 深度分析文档

## 1. 整体架构概述

### 1.1 策略类型
- **策略名称**: Gold Trade Setup Strategy
- **平台**: TradingView Pine Script (需转换为MT5 MQL5)
- **策略类型**: 趋势跟踪策略，结合自适应移动平均线和SuperTrend指标
- **交易品种**: 主要针对黄金(Gold)交易设计
- **时间框架**: 适用于多种时间框架

### 1.2 核心组件架构
```
Gold Trade Setup Strategy
├── AMA (Adaptive Moving Average) 模块
├── SuperTrend 指标模块  
├── 交易信号生成模块
├── 风险管理模块
└── 月相计算模块 (辅助功能)
```

## 2. 核心功能模块详细分析

### 2.1 AMA (自适应移动平均线) 模块

#### 2.1.1 输入参数
- `length`: AMA长度，默认14，最小值1
- `fastLength`: 快速EMA长度，默认2，最小值1  
- `slowLength`: 慢速EMA长度，默认30，最小值1
- `highlightMovements`: 是否高亮AMA移动，默认true
- `src`: AMA计算源数据，默认收盘价

#### 2.1.2 计算逻辑
1. **Alpha系数计算**:
   - `fastAlpha = 2 / (fastLength + 1)`
   - `slowAlpha = 2 / (slowLength + 1)`

2. **效率比率计算**:
   - `hh = ta.highest(src, length + 1)` - 最高价
   - `ll = ta.lowest(src, length + 1)` - 最低价
   - `mltp = hh - ll != 0 ? math.abs(2 * src - ll - hh) / (hh - ll) : 0.0`

3. **平滑常数计算**:
   - `ssc = mltp * (fastAlpha - slowAlpha) + slowAlpha`

4. **AMA值计算**:
   - 初始化: `var float ama = na`
   - 递归计算: `ama := na(ama[1]) ? src : ama[1] + math.pow(ssc, 2) * (src - ama[1])`

#### 2.1.3 可视化
- 颜色逻辑: 上升为绿色，下降为红色
- 线宽: 2像素
- 条件显示: 根据`highlightMovements`参数控制

### 2.2 SuperTrend 指标模块

#### 2.2.1 输入参数
- `atrPeriod`: ATR周期，默认10，最小值1
- `factor`: 倍数因子，默认3.0，最小值0.01，步长0.01

#### 2.2.2 计算逻辑
- 使用Pine Script内置函数: `[supertrend, direction] = ta.supertrend(factor, atrPeriod)`
- 方向判断: `direction > 0` 为上升趋势，`direction <= 0` 为下降趋势
- 首根K线处理: `supertrend := barstate.isfirst ? na : supertrend`

#### 2.2.3 可视化
- 上升趋势线: 绿色，线性断点样式
- 下降趋势线: 红色，线性断点样式  
- 填充区域: 蜡烛体中点与趋势线之间的填充

### 2.3 交易信号生成模块

#### 2.3.1 买入信号逻辑
```pinescript
var bool amaGreen = false
amaGreen := ama > ama[1] and close > ama ? true : amaGreen
buyCondition = amaGreen and direction > 0 and direction[1] <= 0
```

**条件分解**:
1. AMA状态维护: 当AMA上升且价格在AMA之上时，设置amaGreen为true
2. 买入触发: amaGreen为true且SuperTrend从下降转为上升

#### 2.3.2 卖出信号逻辑  
```pinescript
var bool amaRed = false
amaRed := ama < ama[1] and close < ama ? true : amaRed
sellCondition = amaRed and direction <= 0 and direction[1] > 0
```

**条件分解**:
1. AMA状态维护: 当AMA下降且价格在AMA之下时，设置amaRed为true
2. 卖出触发: amaRed为true且SuperTrend从上升转为下降

#### 2.3.3 **重要发现 - 交易方向错误**
在原始代码中存在逻辑错误:
- 买入条件触发时执行的是: `strategy.entry("SELL", strategy.short)` (做空)
- 卖出条件触发时执行的是: `strategy.entry("BUY", strategy.long)` (做多)

这是**反向交易逻辑**，需要在MQL5转换中修正。

### 2.4 风险管理模块

#### 2.4.1 输入参数
- `targetBox`: 目标水平倍数，默认3.0，最小值1.0，步长0.1
- `riskBox`: 风险水平倍数，默认1.0，最小值0.1，步长0.1

#### 2.4.2 价格计算逻辑
**买入信号时**:
- `entryPrice = close`
- `targetPrice = entryPrice + (entryPrice - low) * targetBox`
- `stopPrice = entryPrice - (entryPrice - low) * riskBox`

**卖出信号时**:
- `entryPrice = close`  
- `targetPrice = entryPrice - (high - entryPrice) * targetBox`
- `stopPrice = entryPrice + (high - entryPrice) * riskBox`

### 2.5 月相计算模块

#### 2.5.1 参数设置
- `moonCycle = 29.53`: 平均月球周期天数
- `newMoonBase = timestamp(2023, 1, 21, 0, 0)`: 已知新月日期基准

#### 2.5.2 计算逻辑
- `daysSinceNewMoon = (timenow - newMoonBase) / (24 * 60 * 60 * 1000)`
- `phase = daysSinceNewMoon % moonCycle`
- `isAmavasya = phase < 1 or phase > moonCycle - 1` (新月)
- `isPurnima = phase > moonCycle / 2 - 1 and phase < moonCycle / 2 + 1` (满月)

#### 2.5.3 可视化
- 新月期间: 黑色背景高亮 (透明度90%)
- 满月期间: 白色背景高亮 (透明度90%)

## 3. 数据状态管理

### 3.1 状态变量
- `var float ama = na`: AMA值的持久化存储
- `var bool amaGreen = false`: AMA绿色状态标志
- `var bool amaRed = false`: AMA红色状态标志
- `var float moonCycle = 29.53`: 月球周期常量
- `var float newMoonBase`: 新月基准时间戳

### 3.2 数据依赖关系
```
价格数据 (OHLC) 
    ↓
AMA计算 → amaGreen/amaRed状态
    ↓
SuperTrend计算 → direction状态  
    ↓
交易信号生成 → 订单执行
```

## 4. MQL5转换关键要点

### 4.1 必须实现的MQL5事件函数
- `OnInit()`: 初始化参数和变量
- `OnTick()`: 主要计算和交易逻辑
- `OnDeinit()`: 清理资源

### 4.2 关键转换映射
- Pine Script `input.*` → MQL5 `input` 参数
- Pine Script `ta.highest/lowest` → MQL5 `iHighest/iLowest`
- Pine Script `strategy.entry` → MQL5 `OrderSend`
- Pine Script `plot` → MQL5 图形对象或指标缓冲区

### 4.3 需要修正的问题
1. **交易方向错误**: 买入信号应执行做多，卖出信号应执行做空
2. **时间戳处理**: Pine Script的时间函数需要转换为MQL5的时间处理
3. **状态变量管理**: 需要使用MQL5的全局变量或静态变量

### 4.4 技术指标实现
- AMA需要自定义实现 (MQL5没有内置AMA)
- SuperTrend需要基于ATR自定义实现
- 月相计算需要使用MQL5的时间函数重新实现

## 5. 预期功能等价性验证点

1. **AMA计算精度**: 确保MQL5版本的AMA值与Pine Script版本完全一致
2. **SuperTrend信号**: 验证趋势转换信号的时机和准确性
3. **交易信号触发**: 确保买卖信号在相同市场条件下同步触发
4. **风险管理计算**: 验证止损和目标价格的计算精度
5. **月相计算**: 确保新月和满月的识别准确性

这份分析文档将作为后续MQL5代码生成和验证的基础参考。

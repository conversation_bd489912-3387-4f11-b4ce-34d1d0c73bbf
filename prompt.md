# 角色

你是一位拥有20年经验的顶尖软件架构师和金融量化策略专家。你不仅深度精通 MetaTrader 5 (MT5) 平台和 MQL5 语言，更具备一套严谨的、自动化的开发与验证工作流。你的核心能力是：接收需求，生成代码，然后通过一个“审查-修正”的闭环，不断迭代优化，直至代码完美无瑕。

# 背景

我将提供一段 MQL5 源代码。你的任务是执行一个全自动、带自我修正功能的代码重构与生成流程。最终目标是交付一份功能与原始代码完全等价、经过多轮验证、并且有完整文档和审查报告的新 MQL5 代码。

# 任务指令

请严格按照以下阶段顺序执行任务。其中，**阶段三、四、五是核心的迭代循环**，请务必在循环中执行，直到满足退出条件。

### 阶段一：准备工作

在当前目录下，创建一个新的文件夹，用于存放本次任务生成的所有文件。文件夹名称为 `[请在此处填写新文件夹的名称]`。

---

### 阶段二：深入分析与文档生成 (Analysis & Documentation)

1.  **输入**: 在本指令末尾 `--- 在此下方粘贴您的原始代码 ---` 分割线之后提供的 MQL5 源代码 (下文称“原始代码”)。
2.  **任务**:
    * 深入、完整地分析“原始代码”的每一处细节，包括其整体架构、核心功能、MQL5 特定事件处理、交易逻辑、技术指标使用和数据状态管理。
3.  **输出**:
    * 在第一步创建的文件夹内，生成一份 **极其详细和精确** 的 `analysis_documentation.md` 文件。这份文档是后续所有工作的基础，不得有任何遗漏。

---

### 核心工作循环 (Core Work Loop)

现在，你将进入一个循环工作模式。你将重复执行 **生成 -> 审查 -> 修正** 的步骤，直到代码完美为止。

#### 步骤 3: 代码生成 (首次执行)

1.  **输入**: `analysis_documentation.md` 文件。
2.  **任务**:
    * 根据文档，生成一份全新的、高质量的 MQL5 代码。
    * **生成的代码必须是为 MetaTrader 5 (MT5) 平台服务的、语法完全正确的 MQL5 代码。**
3.  **输出**:
    * 在工作文件夹内，生成一个新的 `.mq5` 代码文件。文件名为 `[请在此处填写生成的MQL5文件名，例如：GeneratedEA.mq5]` (下文称“新代码”)。

#### 步骤 4: 全面审查 (Comprehensive Review)

1.  **输入**: `analysis_documentation.md`, “新代码”, “原始代码”。
2.  **任务**:
    * 创建或更新 `review_report.md` 文件。
    * **(4a) 文档与新代码一致性审查**: 批判性地比对 `analysis_documentation.md` 和“新代码”，检查功能完整性、逻辑准确性和 MQL5 规范性。
    * **(4b) 新旧代码功能等价性审查**: 深度对比“新代码”和“原始代码”，确认在相同输入下，两者的功能行为（如交易、指标计算）是否完全等价。
3.  **输出**:
    * 在 `review_report.md` 文件中，清晰地记录以上两部分审查的结果。
    * **关键判断**:
        * 如果发现 **任何** 不一致、错误或潜在问题，请在报告中以“**问题列表**”的形式明确列出。
        * 如果未发现任何问题，请在报告的结尾明确写入最终结论：“**审查通过：新代码在功能和逻辑上与文档及原始代码完全一致，未发现任何问题。**”

#### 步骤 5: 判断与修正 (Decision & Correction)

1.  **输入**: `review_report.md` 文件。
2.  **任务**:
    * **检查循环退出条件**: 读取 `review_report.md` 的最终结论。
        * **如果结论是“审查通过”**: 循环结束，任务完成。你可以宣告任务成功并停止工作。
        * **如果报告中包含“问题列表”**: 必须执行修正并继续循环。
    * **执行修正**:
        * **(5a) 修改代码**: 逐一阅读“问题列表”中的每一项，并直接修改“新代码”文件 (`[文件名].mq5`)，以修复所有已发现的问题。
        * **(5b) 清理并准备再审查**: 修改完成后，清空 `review_report.md` 中旧的审查结果（保留问题列表作为历史记录或直接覆盖），然后 **返回并重新执行步骤 4 (全面审查)**。
3.  **循环**: 持续重复“**审查 -> 发现问题 -> 修正 -> 再审查**”的流程，直到某一次“全面审查”后，报告的结论为“审查通过”。

---

### 工作流程摘要

1.  **初始化**: 创建目录，分析原始代码并生成 `analysis_documentation.md`。
2.  **首次生成**: 根据文档生成第一版“新代码”。
3.  **进入循环**:
    * **审查**: 对“新代码”进行全面的双重审查，并生成 `review_report.md`。
    * **判断**: 检查审查报告。
        * **若有问题**: 修改“新代码”以解决报告中的所有问题，然后返回**审查**步骤。
        * **若无问题**: 循环结束。
4.  **完成**: 交付最终版的“新代码”、文档和最终的审查报告。

请现在开始执行。

--- 在此下方粘贴您的原始代码 ---



//@version=5
strategy("Gold Trade Setup Strategy", shorttitle="Gold Trade Setup", overlay=true)

// Inputs for AMA
length = input.int(title="AMA Length", defval=14, minval=1)
fastLength = input.int(title="Fast EMA Length", defval=2, minval=1)
slowLength = input.int(title="Slow EMA Length", defval=30, minval=1)
highlightMovements = input.bool(title="Highlight AMA Movements?", defval=true)
src = input.source(title="Source for AMA", defval=close)

// Inputs for Target and Stop Loss Levels
targetBox = input.float(title="Target Level Multiplier", defval=3.0, minval=1.0, step=0.1)
riskBox = input.float(title="Risk Level Multiplier", defval=1.0, minval=0.1, step=0.1)

// AMA Calculation
fastAlpha = 2 / (fastLength + 1)
slowAlpha = 2 / (slowLength + 1)

hh = ta.highest(src, length + 1)
ll = ta.lowest(src, length + 1)

mltp = hh - ll != 0 ? math.abs(2 * src - ll - hh) / (hh - ll) : 0.0

ssc = mltp * (fastAlpha - slowAlpha) + slowAlpha

var float ama = na
ama := na(ama[1]) ? src : ama[1] + math.pow(ssc, 2) * (src - ama[1])

amaColor = highlightMovements ? (ama > ama[1] ? color.green : color.red) : color.gray
plot(ama, title="AMA", linewidth=2, color=amaColor)

// Inputs for SuperTrend
atrPeriod = input.int(10, "ATR Length", minval=1)
factor = input.float(3.0, "Factor", minval=0.01, step=0.01)

[supertrend, direction] = ta.supertrend(factor, atrPeriod)

supertrend := barstate.isfirst ? na : supertrend
upTrend = plot(direction > 0 ? supertrend : na, "Up Trend", color=color.green, style=plot.style_linebr)
downTrend = plot(direction <= 0 ? supertrend : na, "Down Trend", color=color.red, style=plot.style_linebr)
bodyMiddle = plot(barstate.isfirst ? na : (open + close) / 2, "Body Middle", display=display.none)

fill(bodyMiddle, upTrend, color.new(color.green, 90), fillgaps=false)
fill(bodyMiddle, downTrend, color.new(color.red, 90), fillgaps=false)

// Buy Condition
var bool amaGreen = false
amaGreen := ama > ama[1] and close > ama ? true : amaGreen  // AMA green and price above AMA

buyCondition = amaGreen and direction > 0 and direction[1] <= 0  // SuperTrend turns green after AMA is green

if (buyCondition)
    entryPrice = close
    targetPrice = entryPrice + (entryPrice - low) * targetBox
    stopPrice = entryPrice - (entryPrice - low) * riskBox
    strategy.entry("SELL", strategy.short)


// Sell Condition
var bool amaRed = false
amaRed := ama < ama[1] and close < ama ? true : amaRed  // AMA red and price below AMA

sellCondition = amaRed and direction <= 0 and direction[1] > 0  // SuperTrend turns red after AMA is red

if (sellCondition)
    entryPrice = close
    targetPrice = entryPrice - (high - entryPrice) * targetBox
    stopPrice = entryPrice + (high - entryPrice) * riskBox
    strategy.entry("BUY", strategy.long)
    

// Alerts for SuperTrend
alertcondition(direction[1] > direction, title="Downtrend to Uptrend", message="The Supertrend value switched from Downtrend to Uptrend")
alertcondition(direction[1] < direction, title="Uptrend to Downtrend", message="The Supertrend value switched from Uptrend to Downtrend")
alertcondition(direction[1] != direction, title="Trend Change", message="The Supertrend value switched from Uptrend to Downtrend or vice versa")
// Moon Phase Calculation
var float moonCycle = 29.53  // Average days in a lunar cycle
var float newMoonBase = timestamp(2023, 1, 21, 0, 0)  // Known New Moon date (January 21, 2023)

daysSinceNewMoon = (timenow - newMoonBase) / (24 * 60 * 60 * 1000)
phase = daysSinceNewMoon % moonCycle  // Moon phase in days

isAmavasya = phase < 1 or phase > moonCycle - 1  // Near day 0 or end of the cycle
isPurnima = phase > moonCycle / 2 - 1 and phase < moonCycle / 2 + 1  // Near day 15

// Plot Amavasya and Purnima
bgcolor(isAmavasya ? color.new(color.black, 90) : na, title="Amavasya Highlight")
bgcolor(isPurnima ? color.new(color.white, 90) : na, title="Purnima Highlight")
